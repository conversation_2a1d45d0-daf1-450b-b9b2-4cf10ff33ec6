"""
i18n configuration module for the Telegram bot.
Initializes and configures the python-i18n library.
"""

import i18n
import yaml
from pathlib import Path
from typing import Optional
from enum import Enum


class Language(Enum):
    """Supported languages"""

    ENGLISH = "en"
    RUSSIAN = "ru"


def _load_translations_manually(locales_path: Path) -> None:
    """
    Manually load translation files.

    Args:
        locales_path: Path to the locales directory
    """
    for locale_code in [lang.value for lang in Language]:
        locale_file = locales_path / f"{locale_code}.yml"
        if locale_file.exists():
            try:
                with open(locale_file, 'r', encoding='utf-8') as f:
                    translations = yaml.safe_load(f)

                if translations:
                    for key, value in translations.items():
                        i18n.add_translation(key, value, locale=locale_code)
            except Exception:
                # Silently ignore errors to avoid cluttering output
                pass


def setup_i18n(default_language: str = "ru") -> None:
    """
    Initialize and configure the i18n system.

    Args:
        default_language: Default language code (en, ru)
    """
    # Get the project root directory
    project_root = Path(__file__).parent.parent
    locales_path = project_root / "locales"

    # Configure i18n
    i18n.set("locale", default_language)
    i18n.set("fallback", "en")  # Fallback to English if translation not found
    i18n.set("enable_memoization", True)  # Cache translations in memory

    # Clear any existing paths and translations
    i18n.load_path.clear()

    # Load translations manually since automatic loading isn't working properly
    _load_translations_manually(locales_path)


def get_text(key: str, locale: Optional[str] = None, **kwargs) -> str:
    """
    Get localized text by key.

    Args:
        key: The translation key to retrieve
        locale: Language code to use (defaults to current locale)
        **kwargs: Format arguments for the text string

    Returns:
        Formatted localized text string
    """
    try:
        if locale:
            # Temporarily switch locale for this translation
            current_locale = i18n.get("locale")
            i18n.set("locale", locale)
            try:
                result = i18n.t(key, **kwargs)
            finally:
                i18n.set("locale", current_locale)
            return result
        else:
            return i18n.t(key, **kwargs)
    except Exception as e:
        # Fallback error handling
        return f"[Translation error for '{key}': {str(e)}]"


def set_locale(locale: str) -> None:
    """
    Set the current locale.

    Args:
        locale: Language code (en, ru)
    """
    i18n.set("locale", locale)


def get_locale() -> str:
    """
    Get the current locale.

    Returns:
        Current language code
    """
    return i18n.get("locale")


def get_available_locales() -> list[str]:
    """
    Get list of available locales.

    Returns:
        List of available language codes
    """
    return [lang.value for lang in Language]


def add_translation(key: str, value: str, locale: Optional[str] = None) -> None:
    """
    Add or update a translation.

    Args:
        key: Translation key
        value: Translation value
        locale: Language code (defaults to current locale)
    """
    if locale is None:
        locale = get_locale()

    i18n.add_translation(key, value, locale=locale)


# Initialize i18n on module import
# This will be called when the module is first imported
def _initialize():
    """Initialize i18n with default settings"""
    setup_i18n()


# Call initialization
_initialize()
