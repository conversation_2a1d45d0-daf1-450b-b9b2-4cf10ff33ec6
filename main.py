import asyncio

from models.tgbot.core import TGBotCore

async def main():
    bot = TGBotCore()
    application = bot.setup_application()

    async with application:
        application.run_polling()
    try:
        # Run the bot until Ctrl+C is pressed
        application.run_polling()
    finally:
        print("\nShutting down gracefully...")
        # Ensure cleanup runs in an event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(bot.shutdown())
        loop.close()


if __name__ == "__main__":
    main()

