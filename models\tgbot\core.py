from telegram import Update
from telegram.ext import (
    Application,
    <PERSON><PERSON><PERSON><PERSON>,
    MessageHandler,
    filters,
    ContextTypes,
)

from aiolimiter import AsyncLimiter

from models.config import config
from models.logging import logger
from models.i18n_config import setup_i18n, get_text, set_locale
from models.tgbot.commands import TGBotCommandsHandler
from models.redis import RedisContextManager
from models.openrouter import OpenRouterClient


class TGBotCore:
    def __init__(self):
        self.config = config
        self.context_manager = RedisContextManager()
        self.openrouter_client = OpenRouterClient()

        self.user_rate_limiter = AsyncLimiter(config.RATE_LIMIT, 60)

        self.cmd_handler = TGBotCommandsHandler(self.context_manager, logger, config)

        self.log = logger.get_logger("TGBotCore")

        # Initialize localization with python-i18n
        setup_i18n(config.DEFAULT_LANGUAGE)
        set_locale(config.DEFAULT_LANGUAGE)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        user = update.effective_user
        user_id = user.id
        user_message = update.message.text

        # Rate limiting
        try:
            async with self.user_rate_limiter:
                pass
        except Exception:
            await update.message.reply_text(get_text("rate_limit_exceeded"))
            return

        # Show typing action
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action="typing",
        )

        # Get context
        conversation = await self.context_manager.get_context(user_id)
        conversation.append({"role": "user", "content": user_message})

        # Generate response
        response = await self.openrouter_client.generate_response(user_id, conversation)

        if response is None:
            await update.message.reply_text(get_text("no_response"))
            return

        # Update context
        conversation.append({"role": "assistant", "content": response})
        await self.context_manager.set_context(user_id, conversation)

        # Send response (split long messages to avoid Telegram limits)
        if len(response) > 4096:
            for i in range(0, len(response), 4096):
                await update.message.reply_text(response[i : i + 4096])
        else:
            await update.message.reply_text(response)

    def setup_application(self) -> Application:
        application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()

        # Register command handlers
        application.add_handler(CommandHandler("start", self.cmd_handler.start))
        application.add_handler(CommandHandler("help", self.cmd_handler.help_command))
        application.add_handler(CommandHandler("reset", self.cmd_handler.reset_context))
        application.add_handler(CommandHandler("model", self.cmd_handler.model_info))

        # Register message handler
        application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )

        # Error handler
        application.add_error_handler(self.error_handler)

        return application

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE):
        self.log.error(f"Update {update} caused error: {context.error}")

        if isinstance(update, Update):
            try:
                await update.message.reply_text(get_text("unexpected_error"))
            except Exception:
                pass  # Prevent error loops

    async def shutdown(self):
        """Properly clean up resources"""
        try:
            await self.context_manager.redis.aclose()
        except Exception as e:
            print(f"Redis shutdown error: {e}")

        try:
            await self.openrouter_client.client.close()  # Close the AsyncOpenAI client
        except Exception as e:
            print(f"OpenRouter client shutdown error: {e}")

        try:
            await self.openrouter_client.session.aclose()  # Close the httpx session
        except Exception as e:
            print(f"OpenRouter session shutdown error: {e}")
