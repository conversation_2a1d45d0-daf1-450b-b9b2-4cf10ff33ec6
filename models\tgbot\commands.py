from telegram import Update
from telegram.ext import ContextTypes
from models.config import Config
from models.i18n_config import get_text
from models.redis import RedisContextManager
from models.logging import MyLogger


class TGBotCommandsHandler:
    def __init__(self, context_manager:<PERSON>is<PERSON>ontextManager, logger:<PERSON><PERSON>ogger, config:Config):
        self.context_manager = context_manager
        self.log = logger.get_logger("TGBotCommands")
        self.config = config

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        try:
            user = update.effective_user
            start_text = get_text("start_greeting", user=user.mention_markdown_v2())
            await update.message.reply_markdown_v2(start_text)
            await self.model_info(update, context)
            await self.help_command(update, context)
        except Exception as e:
            self.log.error(f"Error in start command: {e}")

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        try:
            help_text = get_text("help_text")
            await update.message.reply_markdown_v2(help_text)
        except Exception as e:
            self.log.error(f"Error in help command: {e}")

    async def reset_context(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        try:
            user_id = update.effective_user.id
            await self.context_manager.clear_context(user_id)
            await update.message.reply_text(get_text("context_cleared"))
        except Exception as e:
            self.log.error(f"Error resetting context: {e}")
            await update.message.reply_text(get_text("context_clear_failed"))

    async def model_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        try:
            model_text = get_text("current_model", model_name=self.config.MODEL_NAME)
            await update.message.reply_text(model_text)
        except Exception as e:
            self.log.error(f"Error in model command: {e}")
